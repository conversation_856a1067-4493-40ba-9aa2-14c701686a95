<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jinghang.mapper.ColumnInfoMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.domain.ColumnInfo">
        <id column="column_id" property="id"/>
        <result column="table_name" property="tableName"/>
        <result column="column_name" property="columnName"/>
        <result column="column_type" property="columnType"/>
        <result column="key_type" property="keyType"/>
        <result column="extra" property="extra"/>
        <result column="remark" property="remark"/>
        <result column="not_null" property="notNull"/>
        <result column="list_show" property="listShow"/>
        <result column="form_show" property="formShow"/>
        <result column="form_type" property="formType"/>
        <result column="query_type" property="queryType"/>
        <result column="dict_name" property="dictName"/>
    </resultMap>

    <sql id="Base_Column_List">
        column_id, table_name, column_name, column_type, key_type, extra, remark, not_null, list_show, form_show, form_type, query_type, dict_name
    </sql>

    <select id="getTables" resultType="com.jinghang.domain.dto.TableInfo">
        select table_name, create_time, engine, table_collation as coding, table_comment as remark
        from information_schema.tables
        where table_schema = (select database())
        and table_name like concat('%',#{tableName},'%')
        order by create_time desc
    </select>

    <select id="findByTableNameOrderByIdAsc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from code_column
        where table_name = #{tableName}
        order by column_id
    </select>

    <select id="getColumns" resultMap="BaseResultMap">
        select column_name, if(is_nullable = 'NO', 1, 0) not_null,
               data_type as column_type, column_comment as remark,
               column_key key_type, extra
        from information_schema.columns
        where table_name = #{tableName}
        and table_schema = (select database())
        order by ordinal_position
    </select>
</mapper>